# XXTT2 项目故障排除指南

## 启动前的环境检查

### 检查必需软件是否正确安装

#### PHP检查
```bash
# 检查PHP版本（必须8.1+）
php --version

# 检查PHP扩展
php -m | findstr -i "pdo mysql redis"
```

**期望输出**:
- PHP版本显示8.1.x或更高
- 能看到pdo_mysql、redis等扩展

**如果PHP未安装或版本过低**:
- 下载安装 [PHP 8.1+](https://windows.php.net/download/)
- 或使用XAMPP、WAMP等集成环境

#### Composer检查
```bash
composer --version
```

**如果Composer未安装**:
- 访问 [getcomposer.org](https://getcomposer.org/download/) 下载安装

#### Node.js检查
```bash
node --version
npm --version
```

**如果Node.js未安装或版本过低**:
- 访问 [nodejs.org](https://nodejs.org/) 下载LTS版本

#### Docker检查
```bash
docker --version
docker-compose --version
```

**如果Docker未安装**:
- 下载安装 [Docker Desktop](https://www.docker.com/products/docker-desktop/)

---

## 常见启动问题及解决方案

### 问题1: Composer安装失败

#### 症状
```
composer install
Loading composer repositories with package information
Installing dependencies (including require-dev) from lock file
Your requirements could not be resolved to an installable set of packages.
```

#### 解决方案
```bash
# 方案1: 清理缓存重试
composer clear-cache
composer install

# 方案2: 忽略平台要求
composer install --ignore-platform-reqs

# 方案3: 更新composer
composer self-update
composer install

# 方案4: 删除vendor目录重新安装
rmdir /s vendor
composer install
```

### 问题2: NPM安装失败

#### 症状
```
npm install
npm ERR! peer dep missing
```

#### 解决方案
```bash
# 方案1: 使用legacy模式
npm install --legacy-peer-deps

# 方案2: 清理缓存
npm cache clean --force
rmdir /s node_modules
npm install

# 方案3: 使用yarn替代
npm install -g yarn
yarn install
```

### 问题3: Docker启动失败

#### 症状A: Docker Desktop未启动
```
docker-compose up -d
error during connect: Get "http://%2F%2F.%2Fpipe%2Fdocker_engine/v1.24/containers/json": open //./pipe/docker_engine: The system cannot find the file specified.
```

**解决方案**: 启动Docker Desktop，等待完全启动后重试

#### 症状B: 端口被占用
```
ERROR: for mysql  Cannot start service mysql: Ports are not available: listen tcp 0.0.0.0:3306: bind: Only one usage of each socket address
```

**解决方案**: 修改.env文件中的端口
```env
FORWARD_DB_PORT=3307
APP_PORT=8080
```

#### 症状C: 内存不足
```
docker-compose up -d
ERROR: for laravel.test  Cannot start service laravel.test: insufficient memory
```

**解决方案**: 
- 增加Docker Desktop内存限制（设置 > Resources > Memory）
- 关闭其他占用内存的程序

### 问题4: 数据库迁移失败

#### 症状
```
php artisan migrate
SQLSTATE[HY000] [2002] Connection refused
```

#### 解决方案
```bash
# 1. 检查MySQL容器状态
docker-compose ps

# 2. 如果MySQL未启动，重启服务
docker-compose restart mysql

# 3. 等待MySQL完全启动（约30秒）
timeout /t 30

# 4. 重新运行迁移
./vendor/bin/sail artisan migrate

# 5. 如果仍然失败，检查.env配置
# 确保DB_HOST=mysql, DB_PORT=3306
```

### 问题5: 前端编译失败

#### 症状A: Vite启动失败
```
npm run dev
Error: Cannot find module 'vite'
```

**解决方案**:
```bash
# 重新安装依赖
rmdir /s node_modules
npm install
npm run dev
```

#### 症状B: 端口冲突
```
npm run dev
Port 5173 is already in use
```

**解决方案**: 修改vite.config.js
```javascript
export default defineConfig({
    server: {
        port: 5174, // 改为其他端口
    },
    // ... 其他配置
});
```

### 问题6: 队列服务启动失败

#### 症状
```
php artisan horizon
Redis connection refused
```

#### 解决方案
```bash
# 1. 检查Redis容器
docker-compose ps

# 2. 重启Redis
docker-compose restart redis

# 3. 检查.env中Redis配置
# REDIS_HOST=redis
# REDIS_PORT=6379

# 4. 重新启动horizon
./vendor/bin/sail artisan horizon
```

---

## 网站访问问题

### 问题1: 页面显示空白

#### 可能原因及解决方案
```bash
# 1. 检查Laravel日志
tail -f storage/logs/laravel.log

# 2. 检查Apache/Nginx错误日志
docker-compose logs laravel.test

# 3. 确保storage目录有写权限
./vendor/bin/sail exec laravel.test chmod -R 775 storage bootstrap/cache

# 4. 清理缓存
./vendor/bin/sail artisan cache:clear
./vendor/bin/sail artisan config:clear
./vendor/bin/sail artisan view:clear
```

### 问题2: 样式文件加载失败

#### 症状
页面显示但没有样式，看起来很丑

#### 解决方案
```bash
# 1. 确保Vite开发服务器运行
npm run dev

# 2. 检查.env中的APP_URL
# APP_URL=http://localhost

# 3. 如果使用生产模式，重新编译
npm run build

# 4. 清理浏览器缓存
# Ctrl+F5 强制刷新页面
```

### 问题3: 管理后台无法访问

#### 症状
访问 /admin 显示404错误

#### 解决方案
```bash
# 1. 确保Filament已安装
composer show filament/filament

# 2. 发布Filament资源
./vendor/bin/sail artisan filament:install

# 3. 创建管理员用户
./vendor/bin/sail artisan make:filament-user

# 4. 清理路由缓存
./vendor/bin/sail artisan route:clear
```

---

## 性能问题排查

### 问题1: 网站加载很慢

#### 排查步骤
```bash
# 1. 检查数据库查询
# 在.env中启用查询日志
DB_LOG_QUERIES=true

# 2. 检查缓存状态
./vendor/bin/sail artisan cache:clear
./vendor/bin/sail redis-cli ping

# 3. 检查队列状态
./vendor/bin/sail artisan horizon:status

# 4. 监控系统资源
docker stats
```

### 问题2: 内存使用过高

#### 解决方案
```bash
# 1. 优化Composer自动加载
composer dump-autoload --optimize

# 2. 启用OPcache（生产环境）
# 在php.ini中启用opcache

# 3. 配置适当的内存限制
# 在.env中设置
MEMORY_LIMIT=512M

# 4. 使用队列处理重任务
./vendor/bin/sail artisan queue:work --memory=128
```

---

## 数据库相关问题

### 问题1: 游戏服务器数据库连接失败

#### 症状
网站显示游戏数据为空或连接错误

#### 解决方案
```bash
# 1. 检查config/app.php中的数据库配置
# 确保游戏服务器信息正确

# 2. 测试连接
./vendor/bin/sail artisan tinker
# 在tinker中运行：
# DB::connection('lin2world')->getPdo();

# 3. 检查防火墙设置
# 确保可以访问游戏服务器的数据库端口

# 4. 检查SQL Server驱动（如果使用PTS）
# 确保安装了sqlsrv扩展
```

### 问题2: 数据同步问题

#### 症状
Web用户数据与游戏数据不一致

#### 解决方案
```bash
# 1. 运行数据同步命令（如果有）
./vendor/bin/sail artisan sync:game-accounts

# 2. 检查同步配置
# 在config/app.php中检查：
# 'sync_game_accounts' => true,

# 3. 查看同步日志
tail -f storage/logs/laravel.log | grep -i sync
```

---

## 开发环境优化

### 提高开发效率的配置

#### 1. 启用调试工具
```bash
# 安装Laravel Debugbar
composer require barryvdh/laravel-debugbar --dev

# 安装Telescope（可选）
composer require laravel/telescope --dev
./vendor/bin/sail artisan telescope:install
./vendor/bin/sail artisan migrate
```

#### 2. 配置IDE支持
```bash
# 生成IDE辅助文件
composer require --dev barryvdh/laravel-ide-helper
./vendor/bin/sail artisan ide-helper:generate
./vendor/bin/sail artisan ide-helper:models
```

#### 3. 设置文件监控
```bash
# 使用nodemon监控PHP文件变化
npm install -g nodemon
nodemon --exec "./vendor/bin/sail artisan serve" --ext php
```

---

## 日志和监控

### 重要日志文件位置
- **Laravel应用日志**: `storage/logs/laravel.log`
- **Docker容器日志**: `docker-compose logs [service_name]`
- **Nginx/Apache日志**: 通过Docker容器查看
- **MySQL日志**: 通过Docker容器查看

### 常用监控命令
```bash
# 实时查看应用日志
tail -f storage/logs/laravel.log

# 查看Docker容器状态
docker-compose ps

# 查看容器资源使用
docker stats

# 查看队列状态
./vendor/bin/sail artisan horizon:status

# 查看数据库连接
./vendor/bin/sail artisan db:monitor
```

---

## 紧急恢复步骤

### 如果项目完全无法启动

#### 完全重置步骤
```bash
# 1. 停止所有服务
docker-compose down

# 2. 清理Docker资源
docker system prune -a

# 3. 删除依赖目录
rmdir /s vendor
rmdir /s node_modules

# 4. 重新安装
composer install
npm install

# 5. 重新配置环境
copy .env.example .env
php artisan key:generate

# 6. 重新启动
docker-compose up -d
./vendor/bin/sail artisan migrate
npm run dev
```

### 数据库恢复
```bash
# 如果有备份文件
./vendor/bin/sail exec mysql mysql -u root -ppassword lin2web_cms < backup.sql

# 重新运行迁移
./vendor/bin/sail artisan migrate:fresh --seed
```

---

## 获取帮助

### 查看系统信息
```bash
# Laravel环境信息
./vendor/bin/sail artisan about

# PHP配置信息
php -i

# 系统资源信息
systeminfo
```

### 联系支持时需要提供的信息
1. 错误信息截图
2. 相关日志内容
3. 系统环境信息
4. 复现步骤
5. 预期行为描述

---

*故障排除指南 - 最后更新: 2025-07-29*
