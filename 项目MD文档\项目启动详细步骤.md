# XXTT2 项目启动详细步骤指南

## 项目架构说明

### 这是什么项目？
这是一个 **Lineage 2 游戏的Web管理系统**，包含：
- 用户注册登录网站
- 游戏充值系统  
- 玩家数据统计
- 管理后台面板

### 技术栈解释
- **Laravel (PHP框架)**: 类似Java的Spring Boot，用于后端开发
- **Filament**: Laravel的管理面板，提供漂亮的后台界面
- **Vite**: 前端构建工具，处理CSS/JS文件
- **Alpine.js**: 轻量级前端框架，添加页面交互
- **TailwindCSS**: CSS框架，用于页面样式

### Java服务器说明
- **你不需要开发Java代码**
- Java服务器是游戏本身的服务端程序（已存在）
- 这个Web项目只是连接游戏数据库，提供网站功能
- 你只需要启动这个PHP项目即可

---

## 环境要求检查

### 必需软件清单
- [ ] **PHP 8.1+** - 后端运行环境
- [ ] **Composer** - PHP包管理器
- [ ] **Node.js 16+** - 前端工具运行环境
- [ ] **Docker Desktop** - 容器化运行环境
- [ ] **Git** - 版本控制工具

### 软件安装检查
打开命令行（PowerShell或CMD），运行以下命令检查：

```bash
# 检查PHP版本（需要8.1+）
php --version

# 检查Composer
composer --version

# 检查Node.js版本（需要16+）
node --version
npm --version

# 检查Docker
docker --version
docker-compose --version

# 检查Git
git --version
```

**如果任何命令报错，请先安装对应软件**

---

## 第一步：项目准备

### 1.1 进入项目目录
```bash
# 打开PowerShell或CMD，进入项目目录
cd C:\Users\<USER>\Desktop\work\flyXxtt2

# 确认当前目录正确
dir
# 应该能看到 composer.json, package.json, docker-compose.yml 等文件
```

### 1.2 检查项目文件
确认以下重要文件存在：
- [ ] `composer.json` - PHP依赖配置
- [ ] `package.json` - 前端依赖配置  
- [ ] `docker-compose.yml` - Docker服务配置
- [ ] `.env.example` - 环境配置模板
- [ ] `artisan` - Laravel命令行工具

---

## 第二步：安装项目依赖

### 2.1 安装PHP依赖包
```bash
# 安装Laravel和其他PHP包（可能需要几分钟）
composer install

# 如果遇到内存不足错误，运行：
composer install --no-dev --optimize-autoloader
```

**预期结果**: 
- 创建 `vendor` 文件夹
- 显示 "Generating optimized autoload files" 等信息
- 最后显示安装成功信息

### 2.2 安装前端依赖包
```bash
# 安装Node.js包（可能需要几分钟）
npm install

# 如果安装失败，可以尝试：
npm install --legacy-peer-deps
```

**预期结果**:
- 创建 `node_modules` 文件夹
- 显示安装的包数量和时间

---

## 第三步：环境配置

### 3.1 创建环境配置文件
```bash
# 复制环境配置模板
copy .env.example .env

# 生成应用密钥（重要！）
php artisan key:generate
```

### 3.2 编辑.env文件
用文本编辑器（如记事本、VS Code）打开 `.env` 文件，确保以下配置：

```env
# 应用基础配置
APP_NAME=XXTT2
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost

# 数据库配置（Docker自动处理，不要修改）
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=lin2web_cms
DB_USERNAME=sail
DB_PASSWORD=password

# 缓存配置
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis配置（Docker自动处理）
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# 邮件配置（开发环境）
MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null

# 其他配置保持默认即可
```

**重要提示**: 
- 不要修改数据库相关配置，Docker会自动处理
- APP_KEY 应该已经通过 `php artisan key:generate` 自动生成

---

## 第四步：启动Docker服务

### 4.1 启动Docker Desktop
- 确保Docker Desktop已启动并运行
- 等待Docker完全启动（系统托盘图标不再转动）

### 4.2 启动项目服务
```bash
# 启动所有服务（MySQL数据库、Redis缓存、Web服务器）
./vendor/bin/sail up -d

# 如果上面命令在Windows上不工作，使用：
vendor\bin\sail.bat up -d

# 或者直接使用docker-compose：
docker-compose up -d
```

**预期结果**:
- 下载Docker镜像（首次运行需要时间）
- 启动3个容器：laravel.test、mysql、redis
- 显示 "Container xxx Started" 信息

### 4.3 检查服务状态
```bash
# 查看运行中的容器
docker-compose ps

# 应该看到3个服务都是 "Up" 状态
```

---

## 第五步：数据库初始化

### 5.1 运行数据库迁移
```bash
# 创建数据库表结构
./vendor/bin/sail artisan migrate

# Windows用户如果上面不工作：
vendor\bin\sail.bat artisan migrate

# 或者：
docker-compose exec laravel.test php artisan migrate
```

**预期结果**:
- 显示 "Migrating: xxx" 信息
- 创建用户表、订单表等
- 最后显示 "Migration completed"

### 5.2 导入初始数据（可选）
```bash
# 如果需要导入游戏相关数据
./vendor/bin/sail exec mysql mysql -u root -ppassword lin2web_cms < sql/lin2web_cms.sql

# 或者进入MySQL容器手动导入
docker-compose exec mysql mysql -u root -ppassword
# 然后在MySQL命令行中：
# USE lin2web_cms;
# SOURCE /var/www/html/sql/lin2web_cms.sql;
```

---

## 第六步：编译前端资源

### 6.1 开发模式编译（推荐）
```bash
# 启动开发服务器，实时编译CSS/JS
npm run dev
```

**预期结果**:
- 显示 "VITE" 启动信息
- 显示本地服务器地址（通常是 http://localhost:5173）
- 保持运行状态，不要关闭这个窗口

### 6.2 生产模式编译（可选）
如果不需要实时编译，可以运行：
```bash
# 一次性编译所有资源
npm run build
```

---

## 第七步：启动队列处理

### 7.1 启动队列服务
**打开新的命令行窗口**，运行：
```bash
# 进入项目目录
cd C:\Users\<USER>\Desktop\work\flyXxtt2

# 启动Laravel Horizon队列处理
./vendor/bin/sail artisan horizon

# Windows用户：
vendor\bin\sail.bat artisan horizon

# 或者：
docker-compose exec laravel.test php artisan horizon
```

**预期结果**:
- 显示 "Horizon started successfully"
- 保持运行状态，处理后台任务

---

## 第八步：访问项目

### 8.1 访问地址
在浏览器中打开以下地址：

- **前台网站**: http://localhost
- **管理后台**: http://localhost/admin
- **队列监控**: http://localhost/horizon

### 8.2 首次访问检查
- [ ] 前台页面正常显示
- [ ] 没有报错信息
- [ ] 页面样式正常加载
- [ ] 可以看到注册/登录按钮

---

## 第九步：创建管理员账户

### 9.1 创建管理员用户
```bash
# 创建Filament管理员账户
./vendor/bin/sail artisan make:filament-user

# 按提示输入：
# Name: Admin
# Email: <EMAIL>  
# Password: 输入密码（至少8位）
```

### 9.2 访问管理后台
- 访问: http://localhost/admin
- 使用刚创建的账户登录
- 查看管理面板功能

---

## 启动完成检查清单

### 环境检查
- [ ] PHP 8.1+ 已安装并可用
- [ ] Composer 已安装并可用
- [ ] Node.js 16+ 已安装并可用
- [ ] Docker Desktop 已启动

### 项目检查  
- [ ] `composer install` 执行成功
- [ ] `npm install` 执行成功
- [ ] `.env` 文件已创建并配置
- [ ] `php artisan key:generate` 执行成功

### 服务检查
- [ ] Docker容器全部启动（3个容器）
- [ ] 数据库迁移执行成功
- [ ] 前端资源编译成功（npm run dev 运行中）
- [ ] 队列服务启动成功（horizon 运行中）

### 访问检查
- [ ] http://localhost 可以正常访问
- [ ] http://localhost/admin 可以正常访问
- [ ] http://localhost/horizon 可以正常访问
- [ ] 管理员账户创建成功并可登录

---

## 常见问题解决

### 问题1: Docker命令不工作
**症状**: `./vendor/bin/sail` 命令报错

**解决方案**:
```bash
# Windows用户使用：
vendor\bin\sail.bat up -d

# 或者直接使用docker-compose：
docker-compose up -d
docker-compose exec laravel.test php artisan migrate
```

### 问题2: 端口被占用
**症状**: 启动时提示端口80被占用

**解决方案**:
编辑 `.env` 文件，修改端口：
```env
APP_PORT=8080
```
然后访问 http://localhost:8080

### 问题3: 前端编译失败
**症状**: `npm run dev` 报错

**解决方案**:
```bash
# 删除node_modules重新安装
rmdir /s node_modules
npm install

# 或者使用yarn
npm install -g yarn
yarn install
yarn dev
```

### 问题4: 数据库连接失败
**症状**: 网站显示数据库连接错误

**解决方案**:
```bash
# 检查Docker容器状态
docker-compose ps

# 重启MySQL容器
docker-compose restart mysql

# 等待30秒后重试
```

### 问题5: 权限问题
**症状**: 文件写入权限错误

**解决方案**:
```bash
# 设置storage目录权限
./vendor/bin/sail exec laravel.test chmod -R 775 storage bootstrap/cache
```

---

## 下一步学习建议

### 1. 熟悉项目结构
- 查看 `routes/web.php` - 了解网站路由
- 查看 `app/Http/Controllers` - 了解控制器
- 查看 `resources/views` - 了解页面模板
- 查看 `app/Filament` - 了解管理面板

### 2. 学习Laravel基础
- [Laravel官方文档](https://laravel.com/docs)
- [Laravel中文文档](https://learnku.com/docs/laravel)

### 3. 学习Filament管理面板
- [Filament官方文档](https://filamentphp.com/docs)

### 4. 了解游戏相关功能
- 用户注册登录流程
- 充值系统工作原理
- 游戏数据统计功能

---

**启动完成！现在你可以开始探索和开发这个Lineage 2游戏管理系统了。**

*文档创建时间: 2025-07-29*
