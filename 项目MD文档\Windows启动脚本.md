# Windows系统启动脚本

## 自动化启动脚本

### 一键启动脚本 (start.bat)

创建一个批处理文件来自动化启动过程：

```batch
@echo off
echo ========================================
echo XXTT2 项目启动脚本
echo ========================================
echo.

:: 检查当前目录
if not exist "composer.json" (
    echo 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

:: 检查Docker是否运行
docker version >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker未运行，请启动Docker Desktop
    pause
    exit /b 1
)

echo 1. 检查环境配置...
if not exist ".env" (
    echo 创建.env文件...
    copy .env.example .env
    php artisan key:generate
)

echo 2. 启动Docker服务...
docker-compose up -d

echo 3. 等待数据库启动...
timeout /t 30 /nobreak

echo 4. 运行数据库迁移...
docker-compose exec -T laravel.test php artisan migrate --force

echo 5. 启动前端编译...
start "前端编译" cmd /k "npm run dev"

echo 6. 启动队列服务...
start "队列服务" cmd /k "docker-compose exec laravel.test php artisan horizon"

echo.
echo ========================================
echo 启动完成！
echo 前台地址: http://localhost
echo 管理后台: http://localhost/admin
echo 队列监控: http://localhost/horizon
echo ========================================
echo.
echo 按任意键退出...
pause >nul
```

### 停止服务脚本 (stop.bat)

```batch
@echo off
echo ========================================
echo XXTT2 项目停止脚本
echo ========================================
echo.

echo 1. 停止Docker服务...
docker-compose down

echo 2. 停止前端编译进程...
taskkill /f /im node.exe 2>nul

echo 3. 清理临时文件...
if exist "storage\logs\laravel.log" (
    echo. > storage\logs\laravel.log
)

echo.
echo 服务已停止！
pause
```

### 重置项目脚本 (reset.bat)

```batch
@echo off
echo ========================================
echo XXTT2 项目重置脚本
echo ========================================
echo.
echo 警告: 此操作将删除所有数据和依赖包！
echo.
set /p confirm=确定要继续吗？(y/N): 
if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 1. 停止所有服务...
docker-compose down

echo 2. 删除Docker卷...
docker-compose down -v

echo 3. 删除依赖包...
if exist "vendor" rmdir /s /q vendor
if exist "node_modules" rmdir /s /q node_modules

echo 4. 清理缓存文件...
if exist "bootstrap\cache\*.php" del /q bootstrap\cache\*.php
if exist "storage\logs\*.log" del /q storage\logs\*.log

echo 5. 重新安装依赖...
composer install
npm install

echo 6. 重新配置环境...
copy .env.example .env
php artisan key:generate

echo 7. 重新启动服务...
docker-compose up -d

echo 8. 等待服务启动...
timeout /t 30 /nobreak

echo 9. 运行数据库迁移...
docker-compose exec -T laravel.test php artisan migrate --force

echo.
echo ========================================
echo 重置完成！
echo ========================================
pause
```

## PowerShell脚本版本

### 启动脚本 (start.ps1)

```powershell
# XXTT2 项目启动脚本
Write-Host "========================================" -ForegroundColor Green
Write-Host "XXTT2 项目启动脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# 检查当前目录
if (-not (Test-Path "composer.json")) {
    Write-Host "错误: 请在项目根目录运行此脚本" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 检查Docker
try {
    docker version | Out-Null
} catch {
    Write-Host "错误: Docker未运行，请启动Docker Desktop" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

Write-Host "1. 检查环境配置..." -ForegroundColor Yellow
if (-not (Test-Path ".env")) {
    Write-Host "创建.env文件..." -ForegroundColor Cyan
    Copy-Item ".env.example" ".env"
    php artisan key:generate
}

Write-Host "2. 启动Docker服务..." -ForegroundColor Yellow
docker-compose up -d

Write-Host "3. 等待数据库启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

Write-Host "4. 运行数据库迁移..." -ForegroundColor Yellow
docker-compose exec -T laravel.test php artisan migrate --force

Write-Host "5. 启动前端编译..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm run dev"

Write-Host "6. 启动队列服务..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "docker-compose exec laravel.test php artisan horizon"

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "启动完成！" -ForegroundColor Green
Write-Host "前台地址: http://localhost" -ForegroundColor Cyan
Write-Host "管理后台: http://localhost/admin" -ForegroundColor Cyan
Write-Host "队列监控: http://localhost/horizon" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Read-Host "按回车键退出"
```

### 健康检查脚本 (health-check.ps1)

```powershell
# XXTT2 项目健康检查脚本
Write-Host "========================================" -ForegroundColor Green
Write-Host "XXTT2 项目健康检查" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

$errors = @()

# 检查Docker容器状态
Write-Host "检查Docker容器状态..." -ForegroundColor Yellow
try {
    $containers = docker-compose ps --format json | ConvertFrom-Json
    foreach ($container in $containers) {
        if ($container.State -eq "running") {
            Write-Host "✓ $($container.Service) - 运行中" -ForegroundColor Green
        } else {
            Write-Host "✗ $($container.Service) - $($container.State)" -ForegroundColor Red
            $errors += "$($container.Service) 未运行"
        }
    }
} catch {
    Write-Host "✗ Docker服务检查失败" -ForegroundColor Red
    $errors += "Docker服务异常"
}

# 检查网站访问
Write-Host ""
Write-Host "检查网站访问..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ 前台网站 - 正常访问" -ForegroundColor Green
    } else {
        Write-Host "✗ 前台网站 - HTTP $($response.StatusCode)" -ForegroundColor Red
        $errors += "前台网站访问异常"
    }
} catch {
    Write-Host "✗ 前台网站 - 无法访问" -ForegroundColor Red
    $errors += "前台网站无法访问"
}

# 检查管理后台
try {
    $response = Invoke-WebRequest -Uri "http://localhost/admin" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ 管理后台 - 正常访问" -ForegroundColor Green
    } else {
        Write-Host "✗ 管理后台 - HTTP $($response.StatusCode)" -ForegroundColor Red
        $errors += "管理后台访问异常"
    }
} catch {
    Write-Host "✗ 管理后台 - 无法访问" -ForegroundColor Red
    $errors += "管理后台无法访问"
}

# 检查队列监控
try {
    $response = Invoke-WebRequest -Uri "http://localhost/horizon" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ 队列监控 - 正常访问" -ForegroundColor Green
    } else {
        Write-Host "✗ 队列监控 - HTTP $($response.StatusCode)" -ForegroundColor Red
        $errors += "队列监控访问异常"
    }
} catch {
    Write-Host "✗ 队列监控 - 无法访问" -ForegroundColor Red
    $errors += "队列监控无法访问"
}

# 检查数据库连接
Write-Host ""
Write-Host "检查数据库连接..." -ForegroundColor Yellow
try {
    $dbCheck = docker-compose exec -T laravel.test php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database OK';"
    if ($dbCheck -match "Database OK") {
        Write-Host "✓ 数据库连接 - 正常" -ForegroundColor Green
    } else {
        Write-Host "✗ 数据库连接 - 异常" -ForegroundColor Red
        $errors += "数据库连接异常"
    }
} catch {
    Write-Host "✗ 数据库连接 - 检查失败" -ForegroundColor Red
    $errors += "数据库连接检查失败"
}

# 检查Redis连接
try {
    $redisCheck = docker-compose exec -T redis redis-cli ping
    if ($redisCheck -match "PONG") {
        Write-Host "✓ Redis连接 - 正常" -ForegroundColor Green
    } else {
        Write-Host "✗ Redis连接 - 异常" -ForegroundColor Red
        $errors += "Redis连接异常"
    }
} catch {
    Write-Host "✗ Redis连接 - 检查失败" -ForegroundColor Red
    $errors += "Redis连接检查失败"
}

# 总结
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
if ($errors.Count -eq 0) {
    Write-Host "✓ 所有检查通过！项目运行正常" -ForegroundColor Green
} else {
    Write-Host "✗ 发现 $($errors.Count) 个问题:" -ForegroundColor Red
    foreach ($error in $errors) {
        Write-Host "  - $error" -ForegroundColor Red
    }
}
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Read-Host "按回车键退出"
```

## 使用说明

### 批处理文件使用方法

1. **创建启动脚本**:
   - 在项目根目录创建 `start.bat` 文件
   - 复制上面的批处理代码
   - 双击运行即可启动项目

2. **创建停止脚本**:
   - 创建 `stop.bat` 文件
   - 复制停止脚本代码
   - 需要停止服务时双击运行

3. **创建重置脚本**:
   - 创建 `reset.bat` 文件
   - 复制重置脚本代码
   - 项目出现问题时使用

### PowerShell脚本使用方法

1. **启用PowerShell脚本执行**:
   ```powershell
   # 以管理员身份运行PowerShell
   Set-ExecutionPolicy RemoteSigned
   ```

2. **创建PowerShell脚本**:
   - 创建 `.ps1` 文件
   - 复制对应的PowerShell代码
   - 右键选择"使用PowerShell运行"

### 脚本功能说明

- **start.bat/start.ps1**: 一键启动所有服务
- **stop.bat**: 停止所有服务
- **reset.bat**: 完全重置项目
- **health-check.ps1**: 检查项目健康状态

### 注意事项

1. **权限问题**: 确保脚本有足够权限访问文件和运行Docker命令
2. **路径问题**: 脚本必须在项目根目录运行
3. **Docker状态**: 确保Docker Desktop已启动
4. **端口占用**: 如果端口被占用，修改.env文件中的端口配置

### 自定义配置

可以根据需要修改脚本中的以下参数：
- 等待时间（timeout值）
- 端口号
- 服务名称
- 检查URL

这些脚本可以大大简化项目的启动和管理过程，特别适合不熟悉命令行的用户使用。

---

*Windows启动脚本 - 最后更新: 2025-07-29*
